# -*- coding: utf-8 -*-
import os
from fastapi import FastAPI, File, UploadFile, HTTPException, Form, Request
from fastapi.responses import HTMLResponse, JSONResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from sqlalchemy import func
import pandas as pd
import random
from models import SessionLocal, Person, Question, Extraction, Task, TaskPerson, TaskQuestion
from typing import List, Optional
from starlette.requests import Request
import tempfile
import shutil
import logging
import io
from datetime import datetime, date

# 配置日志记录
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI()

# 配置模板目录
templates = Jinja2Templates(directory="templates")

# 用于跟踪上传状态
class UploadStatus:
    def __init__(self):
        self.persons_uploaded = False
        self.questions_uploaded = False

upload_status = UploadStatus()

app.mount("/static", StaticFiles(directory="static"), name="static")

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/check-uploads")
async def check_uploads():
    return {
        "persons_uploaded": upload_status.persons_uploaded,
        "questions_uploaded": upload_status.questions_uploaded
    }

@app.post("/upload/persons")
async def upload_persons(file: UploadFile = File(...)):
    if not file.filename.endswith('.xlsx'):
        raise HTTPException(status_code=400, detail="Only .xlsx files are allowed")
    
    temp_path = None
    try:
        logger.info(f"开始处理人员文件上传: {file.filename}")
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as temp_file:
            contents = await file.read()
            temp_file.write(contents)
            temp_path = temp_file.name
            logger.info(f"临时文件已创建: {temp_path}")
        
        # 读取Excel文件
        logger.info("开始读取Excel文件")
        df = pd.read_excel(temp_path)
        logger.info(f"Excel文件读取成功，数据行数: {len(df)}")
        
        # 验证必要的列是否存在
        required_columns = ['姓名', '机构', '职务']
        if not all(col in df.columns for col in required_columns):
            logger.error(f"缺少必要的列: {', '.join(required_columns)}")
            raise HTTPException(
                status_code=400,
                detail=f"Excel file must contain columns: {', '.join(required_columns)}"
            )
        
        # 获取数据库连接
        db = next(get_db())
        try:
            logger.info("开始数据库操作")
            # 清除现有数据
            db.query(Person).delete()
            
            # 插入新数据
            for _, row in df.iterrows():
                person = Person(name=row['姓名'], department=row['机构'], position=row['职务'])
                db.add(person)
            
            db.commit()
            logger.info("数据库操作完成")
            upload_status.persons_uploaded = True
            return {"message": "Persons uploaded successfully"}
        except Exception as db_error:
            logger.error(f"数据库操作失败: {str(db_error)}")
            db.rollback()
            raise HTTPException(status_code=500, detail=str(db_error))
    
    except pd.errors.EmptyDataError:
        logger.error("上传的Excel文件为空")
        raise HTTPException(status_code=400, detail="The uploaded Excel file is empty")
    except Exception as e:
        logger.error(f"处理文件时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if temp_path and os.path.exists(temp_path):
            os.unlink(temp_path)
            logger.info("临时文件已删除")
        await file.close()

@app.post("/upload/questions")
async def upload_questions(file: UploadFile = File(...)):
    if not file.filename.endswith('.xlsx'):
        raise HTTPException(status_code=400, detail="Only .xlsx files are allowed")
    
    temp_path = None
    try:
        logger.info(f"开始处理题目文件上传: {file.filename}")
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as temp_file:
            contents = await file.read()
            temp_file.write(contents)
            temp_path = temp_file.name
            logger.info(f"临时文件已创建: {temp_path}")
        
        # 读取Excel文件
        logger.info("开始读取Excel文件")
        df = pd.read_excel(temp_path)
        logger.info(f"Excel文件读取成功，数据行数: {len(df)}")
        
        # 验证必要的列是否存在
        required_columns = ['题目名称', '所属部门']
        if not all(col in df.columns for col in required_columns):
            logger.error(f"缺少必要的列: {', '.join(required_columns)}")
            raise HTTPException(
                status_code=400,
                detail=f"Excel file must contain columns: {', '.join(required_columns)}"
            )
        
        # 获取数据库连接
        db = next(get_db())
        try:
            logger.info("开始数据库操作")
            # 清除现有数据
            db.query(Question).delete()
            
            # 插入新数据
            for _, row in df.iterrows():
                question = Question(title=row['题目名称'], department=row['所属部门'])
                db.add(question)
            
            db.commit()
            logger.info("数据库操作完成")
            upload_status.questions_uploaded = True
            return {"message": "Questions uploaded successfully"}
        except Exception as db_error:
            logger.error(f"数据库操作失败: {str(db_error)}")
            db.rollback()
            raise HTTPException(status_code=500, detail=str(db_error))
    
    except pd.errors.EmptyDataError:
        logger.error("上传的Excel文件为空")
        raise HTTPException(status_code=400, detail="The uploaded Excel file is empty")
    except Exception as e:
        logger.error(f"处理文件时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if temp_path and os.path.exists(temp_path):
            os.unlink(temp_path)
            logger.info("临时文件已删除")
        await file.close()

@app.get("/extract", response_class=HTMLResponse)
def extract_page(request: Request):
    return templates.TemplateResponse("extract.html", {"request": request})

@app.get("/extract2", response_class=HTMLResponse)
def extract2_page(request: Request):
    return templates.TemplateResponse("extract2.html", {"request": request})

@app.get("/resolution-test", response_class=HTMLResponse)
def resolution_test_page(request: Request):
    return templates.TemplateResponse("resolution_test.html", {"request": request})

@app.get("/extract/check")
async def check_extract_quota():
    """校验当天是否配置任务以及是否还有名额可以抽取"""
    from datetime import datetime, date

    db = next(get_db())
    today = date.today()

    # 检查当天是否配置了任务
    today_task = db.query(Task).filter(
        Task.status == 'active',
        func.date(Task.created_at) == today
    ).first()

    if not today_task:
        return {
            "available": False,
            "message": "当天没有配置任务"
        }

    # 获取当天任务下所有已抽取记录（只统计当天的抽取记录）
    today_extractions = db.query(Extraction).filter(
        Extraction.task_id == today_task.id,
        func.date(Extraction.extraction_time) == today
    ).all()

    # 获取已抽取的人员ID和题目ID
    today_extracted_person_ids = [ext.person_id for ext in today_extractions]
    today_extracted_question_ids = [ext.question_id for ext in today_extractions]

    # 获取当天任务下所有人员和题目
    person_ids = [tp.person_id for tp in db.query(TaskPerson).filter(TaskPerson.task_id == today_task.id).all()]
    question_ids = [tq.question_id for tq in db.query(TaskQuestion).filter(TaskQuestion.task_id == today_task.id).all()]

    # 获取当天任务中未被抽取的人员和题目
    available_persons = db.query(Person).filter(
        Person.id.in_(person_ids),
        ~Person.id.in_(today_extracted_person_ids)
    ).all()

    available_questions = db.query(Question).filter(
        Question.id.in_(question_ids),
        ~Question.id.in_(today_extracted_question_ids)
    ).all()

    # 检查是否还有可用的人员和题目
    has_persons = len(available_persons) > 0
    has_questions = len(available_questions) > 0

    # 记录详细信息用于调试
    print(f"当天任务ID: {today_task.id}, 任务名: {today_task.name}")
    print(f"任务总人数: {len(person_ids)}")
    print(f"当天已抽取人数: {len(today_extracted_person_ids)}")
    print(f"当天可用人数: {len(available_persons)}")
    print(f"任务总题目数: {len(question_ids)}")
    print(f"当天已抽取题目数: {len(today_extracted_question_ids)}")
    print(f"当天可用题目数: {len(available_questions)}")

    if not has_persons:
        return {
            "available": False,
            "message": '当天所有人员都已被抽取过'
        }

    if not has_questions:
        return {
            "available": False,
            "message": '当天所有题目都已被抽取过'
        }

    return {
        "available": True,
        "message": None,
        "debug_info": {
            "task_name": today_task.name,
            "total_persons": len(person_ids),
            "extracted_persons_today": len(today_extracted_person_ids),
            "available_persons": len(available_persons),
            "total_questions": len(question_ids),
            "extracted_questions_today": len(today_extracted_question_ids),
            "available_questions": len(available_questions)
        }
    }

@app.get("/extract/data")
async def get_extract_data():
    """获取所有人员和题目数据用于抽取滚动显示"""
    db = next(get_db())

    # 检查是否存在活跃的任务
    active_task = db.query(Task).filter(Task.status == 'active').first()
    if not active_task:
        raise HTTPException(status_code=400, detail="当前没有活跃的抽取任务")

    # 检查是否有今天的活跃任务
    from datetime import date, datetime
    today = date.today()
    today_start = datetime.combine(today, datetime.min.time())

    today_active_task = db.query(Task).filter(
        Task.status == 'active',
        Task.created_at >= today_start
    ).first()

    if not today_active_task:
        raise HTTPException(status_code=400, detail="今天没有活跃的抽取任务")

    active_task = today_active_task

    # 获取任务关联的人员和题目
    task_person_ids = [tp.person_id for tp in db.query(TaskPerson).filter(TaskPerson.task_id == active_task.id).all()]
    task_question_ids = [tq.question_id for tq in db.query(TaskQuestion).filter(TaskQuestion.task_id == active_task.id).all()]

    if not task_person_ids or not task_question_ids:
        raise HTTPException(status_code=400, detail="当前任务没有关联的人员或题目")

    persons = db.query(Person).filter(Person.id.in_(task_person_ids)).all()
    questions = db.query(Question).filter(Question.id.in_(task_question_ids)).all()

    # 获取当前任务今天已经抽取过的记录
    today_extractions = db.query(Extraction).filter(
        Extraction.task_id == active_task.id,
        Extraction.extraction_time >= today_start
    ).all()

    # 获取已经抽取过的人员ID和题目ID
    extracted_person_ids = [extraction.person_id for extraction in today_extractions]
    extracted_question_ids = [extraction.question_id for extraction in today_extractions]

    # 过滤掉当前任务今天已经抽取过的人员和题目
    available_persons = [p for p in persons if p.id not in extracted_person_ids]
    available_questions = [q for q in questions if q.id not in extracted_question_ids]

    return {
        "task_id": active_task.id,
        "task_name": active_task.name,
        "persons": [{"id": p.id, "name": p.name, "department": p.department, "position": p.position or "未设置"} for p in available_persons],
        "questions": [{"id": q.id, "title": q.title} for q in available_questions]
    }


@app.post("/extract")
async def extract_api():
    """执行抽取操作，返回最终结果"""
    db = next(get_db())

    # 检查是否存在活跃的任务
    active_task = db.query(Task).filter(Task.status == 'active').first()
    if not active_task:
        raise HTTPException(status_code=400, detail="当前没有活跃的抽取任务")

    # 检查是否有今天的活跃任务
    from datetime import date, datetime
    today = date.today()
    today_start = datetime.combine(today, datetime.min.time())

    today_active_task = db.query(Task).filter(
        Task.status == 'active',
        Task.created_at >= today_start
    ).first()

    if not today_active_task:
        raise HTTPException(status_code=400, detail="今天没有活跃的抽取任务")

    active_task = today_active_task

    # 获取任务关联的人员和题目
    task_person_ids = [tp.person_id for tp in db.query(TaskPerson).filter(TaskPerson.task_id == active_task.id).all()]
    task_question_ids = [tq.question_id for tq in db.query(TaskQuestion).filter(TaskQuestion.task_id == active_task.id).all()]

    if not task_person_ids or not task_question_ids:
        raise HTTPException(status_code=400, detail="当前任务没有关联的人员或题目")

    persons = db.query(Person).filter(Person.id.in_(task_person_ids)).all()
    questions = db.query(Question).filter(Question.id.in_(task_question_ids)).all()

    # 获取当前任务今天已经抽取过的记录
    today_extractions = db.query(Extraction).filter(
        Extraction.task_id == active_task.id,
        Extraction.extraction_time >= today_start
    ).all()

    # 获取已经抽取过的人员ID和题目ID
    extracted_person_ids = [extraction.person_id for extraction in today_extractions]
    extracted_question_ids = [extraction.question_id for extraction in today_extractions]

    # 过滤掉当前任务今天已经抽取过的人员
    available_persons = [p for p in persons if p.id not in extracted_person_ids]
    # 过滤掉当前任务今天已经抽取过的题目
    available_questions = [q for q in questions if q.id not in extracted_question_ids]

    # 检查是否还有可用的人员和题目
    if not available_persons:
        raise HTTPException(status_code=400, detail="当前任务的所有人员今天都已被抽取过")

    if not available_questions:
        raise HTTPException(status_code=400, detail="当前任务的所有题目今天都已被抽取过")

    # 从可用的人员和题目中随机选择
    person = random.choice(available_persons)
    question = random.choice(available_questions)
    extraction = Extraction(person_id=person.id, question_id=question.id, task_id=active_task.id)
    db.add(extraction)
    db.commit()

    return {
        "task_id": active_task.id,
        "task_name": active_task.name,
        "person": {
            "id": person.id,
            "name": person.name,
            "department": person.department,
            "position": person.position or "未设置"
        },
        "question": {
            "id": question.id,
            "title": question.title
        },
        "extraction_time": extraction.extraction_time.strftime('%Y-%m-%d %H:%M:%S') if extraction.extraction_time else ""
    }

@app.get("/start-extract", response_class=HTMLResponse)
def start_extract(request: Request):
    return templates.TemplateResponse("start-extract.html", {"request": request})

# 已移除ppt-extract相关内容

# 任务相关接口
@app.post("/tasks/")
async def create_task(name: str = Form(...)):
    db = next(get_db())
    task = Task(name=name, status='init')
    db.add(task)
    db.commit()
    db.refresh(task)
    return {"id": task.id, "name": task.name, "status": task.status}

# 工具函数：检查任务是否已绑定人员和题目
def check_and_activate_task(db, task_id):
    has_person = db.query(TaskPerson).filter(TaskPerson.task_id == task_id).count() > 0
    has_question = db.query(TaskQuestion).filter(TaskQuestion.task_id == task_id).count() > 0
    task = db.query(Task).filter(Task.id == task_id).first()
    if has_person and has_question and task.status == 'init':
        task.status = 'active'
        db.commit()

@app.put("/tasks/{task_id}")
async def update_task(task_id: int, name: str = Form(...)):
    db = next(get_db())
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    task.name = name
    db.commit()
    return {"id": task.id, "name": task.name, "status": task.status}

@app.delete("/tasks/{task_id}")
async def delete_task(task_id: int):
    db = next(get_db())
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    # 删除关联
    db.query(TaskPerson).filter(TaskPerson.task_id == task_id).delete()
    db.query(TaskQuestion).filter(TaskQuestion.task_id == task_id).delete()
    db.query(Extraction).filter(Extraction.task_id == task_id).delete()
    db.delete(task)
    db.commit()
    return {"message": "Task deleted"}

@app.post("/tasks/{task_id}/void")
async def void_task(task_id: int):
    db = next(get_db())
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    if task.status != 'active':
        raise HTTPException(status_code=400, detail="只有正常状态的任务才能作废")
    task.status = 'voided'
    db.commit()
    return {"id": task.id, "name": task.name, "status": task.status, "message": "任务已作废"}

@app.post("/tasks/{task_id}/activate")
async def activate_task(task_id: int):
    db = next(get_db())
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    if task.status != 'voided':
        raise HTTPException(status_code=400, detail="只有已作废的任务才能启用")
    task.status = 'active'
    db.commit()
    return {"id": task.id, "name": task.name, "status": task.status, "message": "任务已启用"}

@app.post("/tasks/{task_id}/bind-persons")
async def bind_persons(task_id: int, file: UploadFile = File(...)):
    if not file.filename.endswith('.xlsx'):
        raise HTTPException(status_code=400, detail="Only .xlsx files are allowed")
    temp_path = None
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as temp_file:
            contents = await file.read()
            temp_file.write(contents)
            temp_path = temp_file.name
        df = pd.read_excel(temp_path)
        required_columns = ['姓名', '机构', '职务']
        if not all(col in df.columns for col in required_columns):
            raise HTTPException(status_code=400, detail=f"Excel file must contain columns: {', '.join(required_columns)}")
        db = next(get_db())
        # 先删除原有绑定
        db.query(TaskPerson).filter(TaskPerson.task_id == task_id).delete()
        # 绑定新人员
        for _, row in df.iterrows():
            # 使用姓名、机构、职务作为唯一标识
            person = db.query(Person).filter_by(
                name=row['姓名'],
                department=row['机构'],
                position=row['职务']
            ).first()
            if not person:
                person = Person(
                    name=row['姓名'],
                    department=row['机构'],
                    position=row['职务']
                )
                db.add(person)
                db.commit()
                db.refresh(person)
            db.add(TaskPerson(task_id=task_id, person_id=person.id))
        db.commit()
        check_and_activate_task(db, task_id)
        return {"message": "人员绑定成功"}
    finally:
        if temp_path and os.path.exists(temp_path):
            os.unlink(temp_path)
        await file.close()

@app.post("/tasks/{task_id}/bind-questions")
async def bind_questions(task_id: int, file: UploadFile = File(...)):
    if not file.filename.endswith('.xlsx'):
        raise HTTPException(status_code=400, detail="Only .xlsx files are allowed")
    temp_path = None
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as temp_file:
            contents = await file.read()
            temp_file.write(contents)
            temp_path = temp_file.name
        df = pd.read_excel(temp_path)
        required_columns = ['题目名称']
        if not all(col in df.columns for col in required_columns):
            raise HTTPException(status_code=400, detail=f"Excel file must contain columns: {', '.join(required_columns)}")
        db = next(get_db())
        # 先删除原有绑定
        db.query(TaskQuestion).filter(TaskQuestion.task_id == task_id).delete()
        # 绑定新题目
        for _, row in df.iterrows():
            # 只使用题目名称作为唯一标识，不再需要部门字段
            question = db.query(Question).filter_by(title=row['题目名称']).first()
            if not question:
                question = Question(title=row['题目名称'], department=None)
                db.add(question)
                db.commit()
                db.refresh(question)
            db.add(TaskQuestion(task_id=task_id, question_id=question.id))
        db.commit()
        check_and_activate_task(db, task_id)
        return {"message": "题目绑定成功"}
    finally:
        if temp_path and os.path.exists(temp_path):
            os.unlink(temp_path)
        await file.close()

@app.get("/download-template/{template_type}")
async def download_template(template_type: str):
    """下载Excel模板文件"""
    if template_type == "persons":
        # 创建人员模板
        data = {
            '姓名': ['张三', '李四', '王五', '赵六'],
            '机构': ['技术部', '市场部', '人事部', '财务部'],
            '职务': ['技术经理', '市场主管', '人事专员', '财务分析师']
        }
        filename = "人员模板.xlsx"
    elif template_type == "questions":
        # 创建题目模板
        data = {
            '题目名称': ['项目管理经验分享', '市场趋势分析', '团队协作心得', '成本控制方法', '技术创新思路']
        }
        filename = "题目模板.xlsx"
    else:
        raise HTTPException(status_code=400, detail="Invalid template type")

    df = pd.DataFrame(data)
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False)
    output.seek(0)
    headers = {'Content-Disposition': f'attachment; filename="{filename}"'}
    return StreamingResponse(output, media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', headers=headers)

@app.get("/export-extraction/{task_id}")
async def export_extraction(task_id: int):
    db = next(get_db())
    extractions = db.query(Extraction).filter(Extraction.task_id == task_id).all()
    if not extractions:
        raise HTTPException(status_code=404, detail="无抽取结果")
    data = []
    for ext in extractions:
        person = db.query(Person).filter(Person.id == ext.person_id).first()
        question = db.query(Question).filter(Question.id == ext.question_id).first()
        data.append({
            '姓名': person.name if person else '',
            '机构': person.department if person else '',
            '职务': person.position if person else '',
            '题目名称': question.title if question else '',
            '题目部门': question.department if question else '',
            '抽取时间': ext.extraction_time.strftime('%Y-%m-%d %H:%M:%S')
        })
    df = pd.DataFrame(data)
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False)
    output.seek(0)
    headers = {'Content-Disposition': f'attachment; filename="extraction_task_{task_id}.xlsx"'}
    return StreamingResponse(output, media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', headers=headers)

@app.get("/tasks-list")
async def tasks_list():
    db = next(get_db())
    tasks = db.query(Task).order_by(Task.created_at.desc()).all()
    result = []
    for t in tasks:
        result.append({
            "id": t.id,
            "name": t.name,
            "status": t.status,
            "created_at": t.created_at.strftime("%Y-%m-%d %H:%M:%S")
        })
    return result

@app.get("/extractions-list/{task_id}")
async def extractions_list(task_id: int):
    db = next(get_db())
    extractions = db.query(Extraction).filter(Extraction.task_id == task_id).all()
    result = []
    for ext in extractions:
        person = db.query(Person).filter(Person.id == ext.person_id).first()
        question = db.query(Question).filter(Question.id == ext.question_id).first()
        result.append({
            'id': ext.id,
            'person_name': person.name if person else '',
            'person_department': person.department if person else '',
            'person_position': person.position if person else '',
            'question_title': question.title if question else '',
            'extraction_time': ext.extraction_time.strftime('%Y-%m-%d %H:%M:%S')
        })
    return result

@app.delete("/extractions/{extraction_id}")
async def delete_extraction(extraction_id: int):
    """删除单个抽取记录"""
    db = next(get_db())
    extraction = db.query(Extraction).filter(Extraction.id == extraction_id).first()
    if not extraction:
        raise HTTPException(status_code=404, detail="抽取记录不存在")

    db.delete(extraction)
    db.commit()
    return {"message": "抽取记录删除成功"}

@app.post("/extractions/delete-batch")
async def delete_extractions_batch(extraction_ids: List[int]):
    """批量删除抽取记录"""
    db = next(get_db())
    deleted_count = db.query(Extraction).filter(Extraction.id.in_(extraction_ids)).delete(synchronize_session=False)
    db.commit()
    return {"message": f"成功删除 {deleted_count} 条抽取记录"}

@app.get("/get-task-id-today")
async def get_task_id_today():
    db = next(get_db())
    today_str = date.today().strftime("%Y%m%d")
    task = db.query(Task).filter(Task.name == today_str).first()
    if not task:
        return {"id": None, "message": "今日任务不存在"}
    return {"id": task.id, "name": task.name}

@app.post("/extract-task/{task_id}")
async def extract_task(task_id: int):
    db = next(get_db())
    # 获取任务下所有已抽取人员ID
    extracted_person_ids = [ext.person_id for ext in db.query(Extraction).filter(Extraction.task_id == task_id).all()]
    # 获取任务下所有人员
    person_ids = [tp.person_id for tp in db.query(TaskPerson).filter(TaskPerson.task_id == task_id).all()]
    available_persons = db.query(Person).filter(Person.id.in_(person_ids), ~Person.id.in_(extracted_person_ids)).all()
    if not available_persons:
        return {"message": "所有人员都已抽取过，可重置任务或查看结果"}
    # 随机抽取人员
    import random
    person = random.choice(available_persons)
    # 获取任务下所有题目
    question_ids = [tq.question_id for tq in db.query(TaskQuestion).filter(TaskQuestion.task_id == task_id).all()]
    questions = db.query(Question).filter(Question.id.in_(question_ids)).all()
    if not questions:
        return {"message": "该任务下无题目"}
    question = random.choice(questions)
    # 写入抽取结果
    extraction = Extraction(person_id=person.id, question_id=question.id, task_id=task_id, extraction_time=datetime.now())
    db.add(extraction)
    db.commit()
    return {
        "person": {"name": person.name, "department": person.department},
        "question": {"title": question.title, "department": question.department},
        "extraction_time": extraction.extraction_time.strftime('%Y-%m-%d %H:%M:%S')
    }

@app.get("/tasks/{task_id}/persons")
async def get_task_persons(task_id: int):
    db = next(get_db())
    person_ids = [tp.person_id for tp in db.query(TaskPerson).filter(TaskPerson.task_id == task_id).all()]
    persons = db.query(Person).filter(Person.id.in_(person_ids)).all()
    return [{"id": p.id, "name": p.name, "department": p.department} for p in persons]

@app.get("/tasks/{task_id}/questions")
async def get_task_questions(task_id: int):
    db = next(get_db())
    question_ids = [tq.question_id for tq in db.query(TaskQuestion).filter(TaskQuestion.task_id == task_id).all()]
    questions = db.query(Question).filter(Question.id.in_(question_ids)).all()
    return [{"id": q.id, "title": q.title, "department": q.department} for q in questions]