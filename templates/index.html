<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>答题系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
            margin: 0;
            background-color: #f0f2f5;
            color: #212529;
            overflow: hidden;
            touch-action: none;
            -webkit-overflow-scrolling: touch;
        }
        .app-wrapper {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 50px;
            background-color: rgb(49,58,67);
            box-shadow: 0 1px 4px rgba(0,21,41,.08);
            z-index: 1001;
            display: flex;
            align-items: center;
            padding: 0 20px;
            font-size: 20px;
            font-weight: 600;
            color: #fff;
        }
        .hamburger-container {
            padding-right: 20px;
            cursor: pointer;
        }
        .hamburger {
            display: inline-block;
            width: 20px;
            height: 14px;
            position: relative;
        }
        .hamburger .line {
            position: absolute;
            height: 2px;
            width: 100%;
            background-color: #fff;
            border-radius: 1px;
            transition: all .3s;
        }
        .hamburger .line-top {
            top: 0;
        }
        .hamburger .line-middle {
            top: 50%;
            transform: translateY(-50%);
        }
        .hamburger .line-bottom {
            bottom: 0;
        }
        .sidebar-collapsed .hamburger .line-top {
            top: 50%;
            transform: translateY(-50%) rotate(45deg);
        }
        .sidebar-collapsed .hamburger .line-middle {
            opacity: 0;
        }
        .sidebar-collapsed .hamburger .line-bottom {
            bottom: 50%;
            transform: translateY(50%) rotate(-45deg);
        }
        .main-container {
            display: flex;
            flex: 1;
            margin-top: 50px;
        }
        .sidebar {
            width: 210px;
            flex-shrink: 0;
            background-color: #394551;
            box-shadow: 2px 0 6px rgba(0,21,41,.35);
            z-index: 1000;
            transition: width 0.28s ease-in-out;
        }
        .app-wrapper.sidebar-collapsed .sidebar {
            width: 0;
            overflow: hidden;
        }
        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 20px 0 0 0;
        }
        .menu-item a {
            display: block;
            padding: 14px 20px;
            color: #fff;
            text-decoration: none;
            background-color: #394551;
            transition: background-color 0.2s;
        }
        .menu-item.active > a {
            background-color: #dc3545 !important;
            color: #fff;
        }
        .menu-item a:hover {
            background-color: #b52a37;
            color: #fff;
        }
        .menu-item.active a {
            color: #fff;
            background-color: #dc3545;
        }
        .app-main {
            flex-grow: 1;
            padding: 20px;
            overflow-y: auto;
            overflow-x: hidden;
            touch-action: pan-y;
        }
        .container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            max-width: 100%;
            margin: 0;
        }
        h1, h2 {
            color: #212529;
            border-bottom: 2px solid #dc3545;
            padding-bottom: 8px;
            margin-bottom: 20px;
        }
        h1 {
            font-size: 24px;
            margin-top: 0;
        }
        button {
            padding: 10px 20px;
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.15s ease-in-out;
        }
        button:hover {
            background-color: #c82333;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .upload-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
        .file-info {
            margin-top: 10px;
            color: #6c757d;
        }
        .result-section, .history-section {
            margin-top: 30px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            display: none;
            background-color: #fff;
        }
        .history-item {
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        input[type="file"] {
            margin-right: 10px;
        }
        .alert {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 15px 20px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            z-index: 1002;
            display: none;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
        }
        .alert-success {
            background-color: #198754;
        }
        .alert-error {
            background-color: #dc3545;
        }
        .sidebar-submenu {
            list-style: none;
            padding-left: 20px;
            margin: 0;
        }
        .sidebar-submenu .menu-item a {
            font-size: 15px;
            padding: 10px 20px;
            background-color: #394551;
        }
        .sidebar-submenu .menu-item.active > a {
            background-color: #dc3545 !important;
            color: #fff;
        }
        .sidebar-submenu .menu-item a:hover {
            background-color: #b52a37;
            color: #fff;
        }
        #task-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0,0,0,0.3);
            z-index: 2000;
            align-items: center;
            justify-content: center;
        }
        #task-modal .modal-content {
            background: #fff;
            padding: 32px 36px 24px 36px;
            border-radius: 12px;
            min-width: 340px;
            max-width: 96vw;
            box-shadow: 0 8px 32px rgba(0,0,0,0.18);
            display: flex;
            flex-direction: column;
            gap: 0;
        }
        #task-modal-title {
            font-size: 22px;
            font-weight: bold;
            margin-bottom: 24px;
            color: #212529;
        }
        #task-form .form-row {
            display: flex;
            align-items: center;
            margin-bottom: 18px;
            gap: 12px;
        }
        #task-form label {
            min-width: 90px;
            color: #394551;
            font-size: 15px;
        }
        #task-form input[type="text"] {
            flex: 1;
            padding: 7px 10px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 15px;
        }
        #task-form input[type="file"] {
            flex: 1;
            font-size: 14px;
        }
        #task-form .upload-btn, #task-form .template-btn {
            padding: 7px 16px;
            border-radius: 4px;
            border: none;
            font-size: 15px;
            cursor: pointer;
            transition: background 0.2s;
        }
        #task-form .upload-btn {
            background: #dc3545;
            color: #fff;
            margin-left: 0;
        }
        #task-form .upload-btn:hover {
            background: #b52a37;
        }
        #task-form .template-btn {
            background: #f0f2f5;
            color: #394551;
            margin-left: 0;
            text-decoration: none;
        }
        #task-form .template-btn:hover {
            background: #e4e7ed;
        }
        #task-form .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 10px;
        }
        #task-form .cancel-btn {
            background: #f0f2f5;
            color: #394551;
        }
        #task-form .cancel-btn:hover {
            background: #e4e7ed;
        }
        #task-form .save-btn {
            background: #dc3545;
            color: #fff;
        }
        #task-form .save-btn:hover {
            background: #b52a37;
        }
        /* 绑定弹窗样式 */
        #bind-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0,0,0,0.3);
            z-index: 2100;
            align-items: center;
            justify-content: center;
        }
        #bind-modal .modal-content {
            background: #fff;
            padding: 32px 36px 24px 36px;
            border-radius: 12px;
            min-width: 340px;
            max-width: 96vw;
            box-shadow: 0 8px 32px rgba(0,0,0,0.18);
            display: flex;
            flex-direction: column;
            gap: 0;
        }
        #bind-modal-title {
            font-size: 22px;
            font-weight: bold;
            margin-bottom: 24px;
            color: #212529;
        }
        #bind-form .form-row {
            display: flex;
            align-items: center;
            margin-bottom: 18px;
            gap: 12px;
        }
        #bind-form label {
            min-width: 90px;
            color: #394551;
            font-size: 15px;
        }
        #bind-form input[type="file"] {
            flex: 1;
            font-size: 14px;
        }
        #bind-form .template-btn {
            background: #f0f2f5;
            color: #394551;
            margin-left: 0;
            text-decoration: none;
        }
        #bind-form .template-btn:hover {
            background: #e4e7ed;
        }
        #bind-form .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 10px;
        }
        #bind-form .cancel-btn {
            background: #f0f2f5;
            color: #394551;
        }
        #bind-form .cancel-btn:hover {
            background: #e4e7ed;
        }
        #bind-form .save-btn {
            background: #dc3545;
            color: #fff;
        }
        #bind-form .save-btn:hover {
            background: #b52a37;
        }
        #task-table th, #task-table td {
            text-align: center;
            vertical-align: middle;
        }
        /* 抽取结果页面样式优化 */
        .page-task-result {
            box-shadow: 0 2px 12px rgba(0,0,0,0.07);
            border-radius: 10px;
            padding: 32px 28px 28px 28px;
            background: #fff;
            margin: 0 auto;
            max-width: 900px;
        }
        .result-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 18px;
        }

        .result-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        #delete-selected-btn {
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            background-color: #dc3545;
        }

        #delete-selected-btn:disabled {
            background-color: #6c757d !important;
            cursor: not-allowed;
            opacity: 0.6;
        }

        #delete-selected-btn:hover:not(:disabled) {
            background-color: #c82333;
        }

        .extraction-checkbox {
            cursor: pointer;
            margin: 0;
            vertical-align: middle;
        }

        #select-all-checkbox {
            cursor: pointer;
            margin: 0;
            vertical-align: middle;
        }

        .delete-single-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            display: inline-block;
            min-width: 40px;
        }

        .delete-single-btn:hover {
            background-color: #c82333;
        }

        /* 抽取结果表格样式 */
        #extraction-table {
            width: 100%;
            background: #f8f9fa;
            border-radius: 6px;
            border-collapse: collapse;
        }

        #extraction-table th {
            text-align: center;
            vertical-align: middle;
            padding: 12px 8px;
            background-color: #e9ecef;
            font-weight: bold;
            border-bottom: 2px solid #dee2e6;
        }

        #extraction-table td {
            text-align: center;
            vertical-align: middle;
            padding: 10px 8px;
            border-bottom: 1px solid #dee2e6;
        }

        #extraction-table tbody tr:hover {
            background-color: #f1f3f4;
        }

        #extraction-table tbody tr:last-child td {
            border-bottom: none;
        }

        /* 空数据提示样式 */
        #extraction-table .empty-message {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 20px;
        }
        .result-header label {
            font-size: 16px;
            color: #394551;
            margin-right: 8px;
        }
        #result-task-select {
            min-width: 180px;
            margin-right: 12px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            font-size: 15px;
            padding: 6px 10px;
        }

        #export-btn {
            margin-left: auto;
            background: #198754;
            color: #fff;
            border: none;
            border-radius: 4px;
            font-size: 15px;
            padding: 8px 20px;
            cursor: pointer;
            transition: background 0.2s;
        }
        #export-btn:hover {
            background: #157347;
        }
        #extraction-table {
            margin-top: 10px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 4px rgba(0,21,41,.06);
        }
        #extraction-table th {
            background: #f0f2f5;
            color: #394551;
            font-weight: 600;
            font-size: 15px;
            height: 44px;
        }
        #extraction-table td {
            background: #fff;
            font-size: 15px;
            height: 42px;
            transition: background 0.15s;
        }
        #extraction-table tbody tr:hover td {
            background: #f7f7fa;
        }
        .extraction-empty {
            text-align: center;
            color: #6c757d;
            font-size: 16px;
            padding: 40px 0 30px 0;
            background: #f8f9fa;
            border-radius: 6px;
            margin-top: 10px;
        }
        .extraction-empty .empty-icon {
            font-size: 36px;
            display: block;
            margin-bottom: 12px;
            color: #adb5bd;
        }
    </style>
</head>
<body>
    <div id="alert" class="alert"></div>

    <div class="app-wrapper">
        <header class="navbar">
            <div class="hamburger-container" onclick="toggleSidebar()">
                <div class="hamburger">
                    <div class="line line-top"></div>
                    <div class="line line-middle"></div>
                    <div class="line line-bottom"></div>
                </div>
            </div>
            <div class="navbar-brand">答题系统</div>
        </header>

        <div class="main-container">
            <aside class="sidebar">
                <ul class="sidebar-menu">
                    <li class="menu-item" id="menu-task" onclick="toggleTaskMenu()">
                        <a href="#">
                            <span>任务管理</span>
                        </a>
                        <ul class="sidebar-menu sidebar-submenu" id="task-submenu" style="display:none;">
                            <li class="menu-item" id="menu-task-query" onclick="event.stopPropagation();showPage('task-query')">
                                <a href="#">任务查询</a>
                            </li>
                            <li class="menu-item" id="menu-task-result" onclick="event.stopPropagation();showPage('task-result')">
                                <a href="#">抽取结果</a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item" id="menu-extract">
                        <a href="/extract" target="_blank">
                            <span>开始抽取</span>
                        </a>
                    </li>
                </ul>
            </aside>

            <main class="app-main">
                <div class="container page-task-query" style="display:none;">
                    <h1>任务查询</h1>
                    <div style="margin-bottom:16px;">
                        <button onclick="showTaskForm('add')">任务新增</button>
                    </div>
                    <div>
                        <table id="task-table" border="0" cellpadding="8" style="width:100%;background:#f8f9fa;border-radius:6px;">
                            <thead>
                                <tr style="background:#f0f2f5">
                                    <th>ID</th>
                                    <th>任务名称</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
                <!-- 任务表单弹窗 -->
                <div id="task-modal" style="display:none;position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.3);z-index:2000;align-items:center;justify-content:center;">
                    <div class="modal-content">
                        <h2 id="task-modal-title">任务</h2>
                        <form id="task-form" onsubmit="submitTaskForm(event)">
                            <input type="hidden" id="task-id">
                            <div class="form-row">
                                <label>任务名称：</label>
                                <input type="text" id="task-name" required>
                            </div>
                            <div id="task-form-error" style="color:#dc3545;font-size:14px;margin-bottom:8px;display:none;"></div>
                            <div class="form-actions">
                                <button type="button" class="cancel-btn" onclick="closeTaskModal()">取消</button>
                                <button type="submit" class="save-btn">保存</button>
                            </div>
                        </form>
                    </div>
                </div>
                <!-- 绑定人员/题目弹窗 -->
                <div id="bind-modal" style="display:none;position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.3);z-index:2100;align-items:center;justify-content:center;">
                    <div class="modal-content">
                        <h2 id="bind-modal-title">绑定</h2>
                        <form id="bind-form" onsubmit="submitBindForm(event)">
                            <input type="hidden" id="bind-task-id">
                            <div class="form-row">
                                <label id="bind-label">上传Excel：</label>
                                <input type="file" id="bind-file" accept=".xlsx">
                                <a id="bind-template" href="#" download class="template-btn">下载模板</a>
                            </div>
                            <div class="form-actions">
                                <button type="button" class="cancel-btn" onclick="closeBindModal()">取消</button>
                                <button type="submit" class="save-btn">上传</button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="container page-task-result" style="display:none;">
                    <h1>抽取结果</h1>
                    <div class="result-header">
                        <div>
                            <label>选择任务：</label>
                            <select id="result-task-select" onchange="loadExtractionResult()"></select>
                        </div>
                        <div class="result-actions">
                            <button id="delete-selected-btn" onclick="deleteSelectedExtractions()" disabled>删除选中</button>
                            <button id="export-btn" onclick="exportExtractionResult()">导出详情</button>
                        </div>
                    </div>
                    <div>
                        <table id="extraction-table">
                            <thead>
                                <tr>
                                    <th style="width: 50px;">
                                        <input type="checkbox" id="select-all-checkbox" onchange="toggleSelectAll()" />
                                    </th>
                                    <th>姓名</th>
                                    <th>部门</th>
                                    <th>职位</th>
                                    <th>题目名称</th>
                                    <th>抽取时间</th>
                                    <th style="width: 80px;">操作</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                        <div id="extraction-empty" class="extraction-empty" style="display:none;">
                            <span class="empty-icon">🗒️</span>
                            暂无抽取结果
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script>
        function toggleSidebar() {
            document.querySelector('.app-wrapper').classList.toggle('sidebar-collapsed');
        }
        function toggleTaskMenu() {
            var submenu = document.getElementById('task-submenu');
            submenu.style.display = submenu.style.display === 'none' ? 'block' : 'none';
        }
        // 任务相关JS
        let currentEditTaskId = null;
        function loadTaskList() {
            fetch('/tasks-list')
                .then(res => res.json())
                .then(data => {
                    const tbody = document.querySelector('#task-table tbody');
                    tbody.innerHTML = '';
                    data.forEach(task => {
                        let statusText = task.status === 'active' ? '正常' : (task.status === 'voided' ? '已作废' : '初始化');
                        let actionBtns = `
                            <button onclick="triggerBindFile(${task.id}, 'persons')">绑定人员</button>
                            <input type="file" id="bind-persons-file-${task.id}" style="display:none" accept=".xlsx" onchange="bindFileUpload(event, ${task.id}, 'persons')">
                            <button onclick="triggerBindFile(${task.id}, 'questions')">绑定题目</button>
                            <input type="file" id="bind-questions-file-${task.id}" style="display:none" accept=".xlsx" onchange="bindFileUpload(event, ${task.id}, 'questions')">
                            <button onclick="deleteTask(${task.id})">删除</button>
                        `;
                        if(task.status === 'active' || task.status === 'voided') {
                            actionBtns += `<button onclick=\"toggleTaskStatus(${task.id}, '${task.status}')\">${task.status === 'active' ? '作废' : '启用'}</button>`;
                        }
                        const tr = document.createElement('tr');
                        tr.innerHTML = `
                            <td>${task.id}</td>
                            <td>${task.name}</td>
                            <td>${statusText}</td>
                            <td>${task.created_at}</td>
                            <td>${actionBtns}</td>
                        `;
                        tbody.appendChild(tr);
                    });
                });
        }
        function showTaskForm(type, id, name) {
            document.getElementById('task-modal').style.display = 'flex';
            if(type === 'add') {
                document.getElementById('task-modal-title').innerText = '新增任务';
                document.getElementById('task-id').value = '';
                document.getElementById('task-name').value = '';
                currentEditTaskId = null;
                // 新增时禁用上传按钮
                // document.getElementById('modal-persons-upload-btn').disabled = true; // Removed
                // document.getElementById('modal-questions-upload-btn').disabled = true; // Removed
            } else {
                document.getElementById('task-modal-title').innerText = '修改任务';
                document.getElementById('task-id').value = id;
                document.getElementById('task-name').value = name;
                currentEditTaskId = id;
                // 编辑时启用上传按钮
                // document.getElementById('modal-persons-upload-btn').disabled = false; // Removed
                // document.getElementById('modal-questions-upload-btn').disabled = false; // Removed
            }
        }
        function closeTaskModal() {
            document.getElementById('task-modal').style.display = 'none';
        }
        function submitTaskForm(e) {
            e.preventDefault();
            const id = document.getElementById('task-id').value;
            const name = document.getElementById('task-name').value;
            const errorDiv = document.getElementById('task-form-error');
            errorDiv.style.display = 'none';
            errorDiv.innerText = '';
            if(id) {
                // 修改
                fetch(`/tasks/${id}`, {
                    method: 'PUT',
                    body: new URLSearchParams({name}),
                }).then(async res => {
                    let data;
                    try { data = await res.json(); } catch { data = { detail: '服务器无响应' }; }
                    if(res.ok && data.id) {
                        closeTaskModal();
                        loadTaskList();
                    } else {
                        errorDiv.style.display = 'block';
                        errorDiv.innerText = data.detail || data.message || '保存失败';
                    }
                }).catch(() => {
                    errorDiv.style.display = 'block';
                    errorDiv.innerText = '网络错误或服务器异常';
                });
            } else {
                // 新增
                fetch('/tasks/', {
                    method: 'POST',
                    body: new URLSearchParams({name}),
                }).then(async res => {
                    let data;
                    try { data = await res.json(); } catch { data = { detail: '服务器无响应' }; }
                    if(res.ok && data.id) {
                        closeTaskModal();
                        loadTaskList();
                    } else {
                        errorDiv.style.display = 'block';
                        errorDiv.innerText = data.detail || data.message || '新增失败';
                    }
                }).catch(() => {
                    errorDiv.style.display = 'block';
                    errorDiv.innerText = '网络错误或服务器异常';
                });
            }
        }
        function showApiMessage(resp) {
            if(resp.message) {
                alert(resp.message);
            } else if(resp.detail) {
                alert(resp.detail);
            } else {
                alert('操作成功');
            }
        }
        function triggerBindFile(taskId, type) {
            let message = '';
            if (type === 'persons') {
                message = '请上传人员Excel文件\n\n必须包含以下字段：\n• 姓名\n• 部门\n• 职位\n\n确定要选择文件吗？';
            } else if (type === 'questions') {
                message = '请上传题目Excel文件\n\n必须包含以下字段：\n• 题目名称\n\n确定要选择文件吗？';
            }

            if (confirm(message)) {
                document.getElementById(`bind-${type}-file-${taskId}`).click();
            }
        }
        function deleteTask(id) {
            if(!confirm('确定要删除该任务吗？')) return;
            fetch(`/tasks/${id}`, {method: 'DELETE'})
                .then(async res => {
                    let data;
                    try { data = await res.json(); } catch { data = { detail: '服务器无响应' }; }
                    showApiMessage(data);
                    loadTaskList();
                })
                .catch(() => {
                    showApiMessage({ detail: '网络错误或服务器异常' });
                });
        }
        function toggleTaskStatus(id, status) {
            if(status === 'active') {
                if(!confirm('确定要作废该任务吗？')) return;
                fetch(`/tasks/${id}/void`, {method: 'POST'})
                    .then(async res => {
                        let data;
                        try { data = await res.json(); } catch { data = { detail: '服务器无响应' }; }
                        showApiMessage(data);
                        loadTaskList();
                    })
                    .catch(() => {
                        showApiMessage({ detail: '网络错误或服务器异常' });
                    });
            } else {
                if(!confirm('确定要启用该任务吗？')) return;
                fetch(`/tasks/${id}/activate`, {method: 'POST'})
                    .then(async res => {
                        let data;
                        try { data = await res.json(); } catch { data = { detail: '服务器无响应' }; }
                        showApiMessage(data);
                        loadTaskList();
                    })
                    .catch(() => {
                        showApiMessage({ detail: '网络错误或服务器异常' });
                    });
            }
        }
        function bindFileUpload(event, taskId, type) {
            const file = event.target.files[0];
            if(!file) return;
            const formData = new FormData();
            formData.append('file', file);
            let url = '';
            if(type === 'persons') {
                url = `/tasks/${taskId}/bind-persons`;
            } else {
                url = `/tasks/${taskId}/bind-questions`;
            }
            fetch(url, {
                method: 'POST',
                body: formData
            })
            .then(async res => {
                let data;
                try { data = await res.json(); } catch { data = { detail: '服务器无响应' }; }
                showApiMessage(data);
                event.target.value = '';
                loadTaskList();
                checkUploads(); // 添加这行来更新上传状态
            })
            .catch(() => {
                showApiMessage({ detail: '网络错误或服务器异常' });
            });
        }
        // 获取当前选中的任务ID (用于绑定Excel时指定任务)
        function getSelectedTaskId() {
            const taskTable = document.getElementById('task-table');
            const selectedRow = taskTable.querySelector('tr.active'); // 假设active类表示选中
            if (selectedRow) {
                return selectedRow.cells[0].textContent; // 获取ID列的文本
            }
            return null;
        }
        // ========== 抽取结果页面相关 ==========
        function loadTaskSelectOptions() {
            fetch('/tasks-list')
                .then(res => res.json())
                .then(data => {
                    const select = document.getElementById('result-task-select');
                    select.innerHTML = '';
                    data.forEach(task => {
                        const option = document.createElement('option');
                        option.value = task.id;
                        option.text = `${task.name}（${task.status === 'active' ? '正常' : '已作废'}）`;
                        select.appendChild(option);
                    });
                    if(data.length > 0) {
                        loadExtractionResult();
                    } else {
                        document.querySelector('#extraction-table tbody').innerHTML = '<tr><td colspan="7" class="empty-message">暂无任务</td></tr>';
                    }
                });
        }
        function loadExtractionResult() {
            const taskId = document.getElementById('result-task-select').value;
            if(!taskId) return;
            fetch(`/extractions-list/${taskId}`)
                .then(res => res.json())
                .then(data => {
                    const tbody = document.querySelector('#extraction-table tbody');
                    const emptyDiv = document.getElementById('extraction-empty');
                    tbody.innerHTML = '';
                    if(data.length === 0) {
                        tbody.innerHTML = '';
                        emptyDiv.style.display = 'block';
                        return;
                    }
                    emptyDiv.style.display = 'none';
                    data.forEach(row => {
                        const tr = document.createElement('tr');
                        tr.innerHTML = `
                            <td>
                                <input type="checkbox" class="extraction-checkbox" value="${row.id}" onchange="updateDeleteButton()" />
                            </td>
                            <td>${row.person_name}</td>
                            <td>${row.person_department}</td>
                            <td>${row.person_position || '未设置'}</td>
                            <td>${row.question_title}</td>
                            <td>${row.extraction_time}</td>
                            <td>
                                <button onclick="deleteExtraction(${row.id})" class="delete-single-btn">删除</button>
                            </td>
                        `;
                        tbody.appendChild(tr);
                    });

                    // 重置全选复选框状态
                    document.getElementById('select-all-checkbox').checked = false;
                    updateDeleteButton();
                });
        }
        function exportExtractionResult() {
            const taskId = document.getElementById('result-task-select').value;
            const tbody = document.querySelector('#extraction-table tbody');
            // 判断是否有数据
            if (!tbody || tbody.children.length === 0) {
                showAlert('暂无可导出的数据', 'error');
                return;
            }
            window.open(`/export-extraction/${taskId}`);
        }

        // 全选/取消全选功能
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('select-all-checkbox');
            const checkboxes = document.querySelectorAll('.extraction-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });

            updateDeleteButton();
        }

        // 更新删除按钮状态
        function updateDeleteButton() {
            const checkboxes = document.querySelectorAll('.extraction-checkbox');
            const checkedBoxes = document.querySelectorAll('.extraction-checkbox:checked');
            const deleteButton = document.getElementById('delete-selected-btn');

            deleteButton.disabled = checkedBoxes.length === 0;

            // 更新全选复选框状态
            const selectAllCheckbox = document.getElementById('select-all-checkbox');
            if (checkedBoxes.length === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (checkedBoxes.length === checkboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
                selectAllCheckbox.checked = false;
            }
        }

        // 删除单个抽取记录
        function deleteExtraction(extractionId) {
            if (!confirm('确定要删除这条抽取记录吗？')) {
                return;
            }

            fetch(`/extractions/${extractionId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                showAlert(data.message, 'success');
                loadExtractionResult(); // 重新加载数据
            })
            .catch(error => {
                console.error('删除失败:', error);
                showAlert('删除失败，请重试', 'error');
            });
        }

        // 批量删除选中的抽取记录
        function deleteSelectedExtractions() {
            const checkedBoxes = document.querySelectorAll('.extraction-checkbox:checked');
            if (checkedBoxes.length === 0) {
                showAlert('请先选择要删除的记录', 'warning');
                return;
            }

            if (!confirm(`确定要删除选中的 ${checkedBoxes.length} 条抽取记录吗？`)) {
                return;
            }

            const extractionIds = Array.from(checkedBoxes).map(checkbox => parseInt(checkbox.value));

            fetch('/extractions/delete-batch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(extractionIds)
            })
            .then(response => response.json())
            .then(data => {
                showAlert(data.message, 'success');
                loadExtractionResult(); // 重新加载数据
            })
            .catch(error => {
                console.error('批量删除失败:', error);
                showAlert('批量删除失败，请重试', 'error');
            });
        }
        // 页面切换时自动加载任务下拉和结果
        function showPage(page) {
            // 菜单高亮
            // 移除首页相关逻辑
            document.getElementById('menu-task').classList.remove('active');
            document.getElementById('menu-task-query').classList.remove('active');
            document.getElementById('menu-task-result').classList.remove('active');
            // 页面显示
            document.querySelector('.page-task-query').style.display = 'none';
            document.querySelector('.page-task-result').style.display = 'none';
            if(page === 'task-query') {
                document.getElementById('menu-task').classList.add('active');
                document.getElementById('menu-task-query').classList.add('active');
                document.querySelector('.page-task-query').style.display = '';
                loadTaskList();
            } else if(page === 'task-result') {
                document.getElementById('menu-task').classList.add('active');
                document.getElementById('menu-task-result').classList.add('active');
                document.querySelector('.page-task-result').style.display = '';
                loadTaskSelectOptions(); // 页面切换时加载任务下拉
            }
        }

        let personsUploaded = false;
        let questionsUploaded = false;
        let extractionHistory = [];

        function showAlert(message, type) {
            const alert = document.getElementById('alert');
            alert.textContent = message;
            alert.className = `alert alert-${type}`;
            alert.style.display = 'block';
            
            setTimeout(() => {
                alert.style.display = 'none';
            }, 3000);
        }

        async function handleUpload(event, type) {
            event.preventDefault();
            const form = event.target;
            const formData = new FormData(form);
            
            try {
                const response = await fetch(`/upload/${type}`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showAlert('上传成功！', 'success');
                    // 立即更新上传状态
                    if (type === 'persons') {
                        document.getElementById('persons-info').textContent = '✓ 人员信息已上传';
                        personsUploaded = true;
                    } else if (type === 'questions') {
                        document.getElementById('questions-info').textContent = '✓ 题目信息已上传';
                        questionsUploaded = true;
                    }
                    // 更新抽取按钮状态
                    document.getElementById('extract-btn').disabled = !(personsUploaded && questionsUploaded);
                } else {
                    showAlert(`上传失败：${result.detail}`, 'error');
                    form.reset();
                }
            } catch (error) {
                showAlert('上传失败：网络错误', 'error');
                form.reset();
            }
        }

        // 检查是否已上传文件
        async function checkUploads() {
            try {
                const response = await fetch('/check-uploads');
                const data = await response.json();
                personsUploaded = data.persons_uploaded;
                questionsUploaded = data.questions_uploaded;
                
                const personsInfo = document.getElementById('persons-info');
                const questionsInfo = document.getElementById('questions-info');
                const extractBtn = document.getElementById('extract-btn');
                
                if (personsInfo) personsInfo.textContent = personsUploaded ? '✓ 人员信息已上传' : '';
                if (questionsInfo) questionsInfo.textContent = questionsUploaded ? '✓ 题目信息已上传' : '';
                if (extractBtn) extractBtn.disabled = !(personsUploaded && questionsUploaded);
            } catch (error) {
                console.error('检查上传状态失败:', error);
            }
        }

        // 开始抽取
        async function startExtraction() {
            const extractBtn = document.getElementById('extract-btn');
            extractBtn.disabled = true;
            
            // 显示动画效果
            const personResult = document.getElementById('person-result');
            const questionResult = document.getElementById('question-result');
            document.getElementById('result-section').style.display = 'block';
            
            // 动画持续5秒
            const duration = 5000;
            const interval = 100;
            const startTime = Date.now();
            
            const animateResults = async () => {
                const elapsed = Date.now() - startTime;
                
                if (elapsed < duration) {
                    try {
                        // 获取随机候选项
                        const response = await fetch('/get-candidates');
                        const candidates = await response.json();
                        
                        personResult.textContent = `抽取中: ${candidates.person}`;
                        questionResult.textContent = `抽取中: ${candidates.question}`;
                        personResult.classList.add('animate');
                        questionResult.classList.add('animate');
                        
                        setTimeout(animateResults, interval);
                    } catch (error) {
                        showAlert('获取候选数据失败', 'error');
                        extractBtn.disabled = false;
                    }
                } else {
                    try {
                        // 获取最终结果
                        const response = await fetch('/extract');
                        const result = await response.json();
                        
                        personResult.classList.remove('animate');
                        questionResult.classList.remove('animate');
                        personResult.textContent = `抽取结果 - 人员: ${result.person}`;
                        questionResult.textContent = `抽取结果 - 题目: ${result.question}`;
                        
                        // 添加到历史记录
                        extractionHistory.push({
                            person: result.person,
                            question: result.question,
                            timestamp: new Date().toLocaleString()
                        });
                        
                        extractBtn.disabled = false;
                    } catch (error) {
                        showAlert('获取抽取结果失败', 'error');
                        extractBtn.disabled = false;
                    }
                }
            };
            
            animateResults();
        }

        // 结束抽取并显示历史结果
        function finishExtraction() {
            const historySection = document.getElementById('history-section');
            const historyList = document.getElementById('history-list');
            
            historySection.style.display = 'block';
            historyList.innerHTML = '';
            
            extractionHistory.forEach((result, index) => {
                const historyItem = document.createElement('div');
                historyItem.className = 'history-item';
                historyItem.innerHTML = `
                    <div>第${index + 1}次抽取 - ${result.timestamp}</div>
                    <div>人员：${result.person}</div>
                    <div>题目：${result.question}</div>
                `;
                historyList.appendChild(historyItem);
            });
        }

        // 页面加载时检查上传状态
        window.onload = checkUploads;
    </script>
</body>
</html>